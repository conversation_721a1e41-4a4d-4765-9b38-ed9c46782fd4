<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无限滚动虚拟列表 - 支持200万条数据</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .search-box {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
        }

        .virtual-list-container {
            height: 500px;
            position: relative;
            overflow: auto;
            border: 1px solid #eee;
        }

        .virtual-list-content {
            position: relative;
        }

        .virtual-list-item {
            position: absolute;
            left: 0;
            right: 0;
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .virtual-list-item:hover {
            background: #f8f9ff;
        }

        .item-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .item-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .item-info {
            flex: 1;
        }

        .item-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .item-subtitle {
            font-size: 12px;
            color: #666;
        }

        .item-actions {
            display: flex;
            gap: 8px;
        }

        .item-btn {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background: white;
            color: #666;
            cursor: pointer;
            font-size: 12px;
        }

        .item-btn:hover {
            background: #f0f0f0;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .performance-info {
            padding: 15px 20px;
            background: #f8f9fa;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
        }

        .highlight {
            background: yellow;
            padding: 1px 2px;
        }

        .chunk-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>无限滚动虚拟列表</h1>
            <div class="stats">
                <span>总数据量: <span id="totalCount">0</span></span>
                <span>可见项目: <span id="visibleCount">0</span></span>
                <span>当前块: <span id="currentChunk">0/0</span></span>
                <span>渲染时间: <span id="renderTime">0ms</span></span>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="generateData(10000)">生成1万条数据</button>
            <button class="btn" onclick="generateData(100000)">生成10万条数据</button>
            <button class="btn" onclick="generateData(2000000)">生成200万条数据</button>
            <input type="text" class="search-box" placeholder="搜索..." id="searchInput">
            <button class="btn" onclick="scrollToTop()">回到顶部</button>
            <button class="btn" onclick="scrollToBottom()">滚动到底部</button>
            <input type="number" class="search-box" placeholder="跳转到..." id="jumpInput" style="width: 100px;">
            <button class="btn" onclick="jumpToItem()">跳转</button>
        </div>

        <div class="virtual-list-container" id="virtualListContainer">
            <div class="virtual-list-content" id="virtualListContent">
                <div class="loading">点击上方按钮生成数据...</div>
            </div>
        </div>

        <div class="performance-info">
            <div>无限滚动模式：分块管理数据，智能预加载，突破CSS高度限制</div>
            <div>滚动位置: <span id="scrollInfo">0</span> | 内存使用: 仅加载可见块</div>
        </div>
    </div>

    <script>
        class InfiniteVirtualList {
            constructor(container, options = {}) {
                this.container = container;
                this.content = container.querySelector('#virtualListContent');
                this.itemHeight = options.itemHeight || 60;
                this.buffer = options.buffer || 5;

                // 无限滚动配置
                this.CHUNK_SIZE = 10000; // 每块数据量
                this.MAX_DOM_HEIGHT = 10000000; // 安全DOM高度限制(10M像素)
                this.PRELOAD_CHUNKS = 2; // 预加载块数
                this.KEEP_CHUNKS = 1; // 保留块数

                // 分段虚拟滚动配置
                this.SEGMENT_MAX_ITEMS = Math.floor(this.MAX_DOM_HEIGHT / this.itemHeight); // 每段最大项目数
                this.currentSegment = 0; // 当前段索引
                this.segmentOffset = 0; // 当前段在整体中的偏移量

                // 数据管理
                this.totalItems = 0;
                this.chunks = new Map(); // 存储数据块
                this.loadedChunks = new Set(); // 已加载的块
                this.currentChunk = 0;
                this.virtualScrollTop = 0; // 虚拟滚动位置

                // 渲染状态
                this.visibleItems = [];
                this.startIndex = 0;
                this.endIndex = 0;
                this.containerHeight = 0;
                this.visibleCount = 0;

                // 初始化分段参数
                this.currentSegment = 0;
                this.segmentOffset = 0;

                // 搜索相关
                this.searchTerm = '';
                this.filteredIndices = null; // 搜索结果索引

                this.init();
            }

            init() {
                this.containerHeight = this.container.clientHeight;
                this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.buffer * 2;

                this.container.addEventListener('scroll', this.handleScroll.bind(this));
                window.addEventListener('resize', this.handleResize.bind(this));
            }

            setData(data) {
                const startTime = performance.now();

                this.totalItems = data.length;
                this.chunks.clear();
                this.loadedChunks.clear();
                this.currentChunk = 0;
                this.virtualScrollTop = 0;
                this.filteredIndices = null;

                // 将数据分块存储
                this.chunkData(data);

                // 加载初始块
                this.loadChunk(0);

                this.updateStats();
                this.render();

                const endTime = performance.now();
                document.getElementById('renderTime').textContent = `${(endTime - startTime).toFixed(2)}ms`;
            }

            chunkData(data) {
                const totalChunks = Math.ceil(data.length / this.CHUNK_SIZE);

                for (let i = 0; i < totalChunks; i++) {
                    const startIndex = i * this.CHUNK_SIZE;
                    const endIndex = Math.min(startIndex + this.CHUNK_SIZE, data.length);
                    const chunkData = data.slice(startIndex, endIndex);

                    this.chunks.set(i, {
                        startIndex,
                        endIndex: endIndex - 1,
                        data: chunkData,
                        loaded: true
                    });
                    this.loadedChunks.add(i);
                }
            }

            loadChunk(chunkIndex) {
                if (this.loadedChunks.has(chunkIndex) || !this.chunks.has(chunkIndex)) return;

                const chunk = this.chunks.get(chunkIndex);
                chunk.loaded = true;
                this.loadedChunks.add(chunkIndex);

                // 如果当前视图需要这个块，重新渲染
                if (this.isChunkVisible(chunkIndex)) {
                    this.render();
                }
            }

            unloadChunk(chunkIndex) {
                if (!this.loadedChunks.has(chunkIndex)) return;

                const chunk = this.chunks.get(chunkIndex);
                if (chunk) {
                    chunk.loaded = false;
                    this.loadedChunks.delete(chunkIndex);
                }
            }

            isChunkVisible(chunkIndex) {
                const chunk = this.chunks.get(chunkIndex);
                if (!chunk) return false;

                const chunkStartPos = chunk.startIndex * this.itemHeight;
                const chunkEndPos = (chunk.endIndex + 1) * this.itemHeight;
                const viewStart = this.virtualScrollTop;
                const viewEnd = this.virtualScrollTop + this.containerHeight;

                return !(chunkEndPos < viewStart || chunkStartPos > viewEnd);
            }

            handleScroll() {
                requestAnimationFrame(() => {
                    this.updateVirtualScrollPosition();
                    this.manageChunks();
                    this.render();
                    this.updateScrollInfo();
                });
            }

            updateVirtualScrollPosition() {
                const scrollTop = this.container.scrollTop;

                // 计算当前段内的虚拟滚动位置
                this.virtualScrollTop = this.segmentOffset + scrollTop;

                // 检查是否需要切换段
                this.checkSegmentBoundary();
            }

            checkSegmentBoundary() {
                const totalItems = this.getTotalItemCount();
                const totalSegments = Math.ceil(totalItems / this.SEGMENT_MAX_ITEMS);

                // 检查是否需要向下切换段
                const currentSegmentEnd = (this.currentSegment + 1) * this.SEGMENT_MAX_ITEMS * this.itemHeight;
                const scrollBuffer = this.containerHeight; // 缓冲区

                if (this.virtualScrollTop + scrollBuffer >= currentSegmentEnd && this.currentSegment < totalSegments - 1) {
                    this.switchToSegment(this.currentSegment + 1);
                    return;
                }

                // 检查是否需要向上切换段
                const currentSegmentStart = this.currentSegment * this.SEGMENT_MAX_ITEMS * this.itemHeight;

                if (this.virtualScrollTop < currentSegmentStart + scrollBuffer && this.currentSegment > 0) {
                    this.switchToSegment(this.currentSegment - 1);
                    return;
                }
            }

            switchToSegment(newSegment) {
                const oldVirtualScrollTop = this.virtualScrollTop;

                this.currentSegment = newSegment;
                this.segmentOffset = newSegment * this.SEGMENT_MAX_ITEMS * this.itemHeight;

                // 计算新段内的滚动位置
                const newScrollTop = oldVirtualScrollTop - this.segmentOffset;

                // 更新容器滚动位置
                this.container.scrollTop = newScrollTop;
                this.virtualScrollTop = oldVirtualScrollTop;

                // 重新渲染
                this.render();
            }

            getTotalVirtualHeight() {
                if (this.filteredIndices) {
                    return this.filteredIndices.length * this.itemHeight;
                }
                return this.totalItems * this.itemHeight;
            }

            getCurrentSegmentHeight() {
                const totalItems = this.getTotalItemCount();
                const segmentStartIndex = this.currentSegment * this.SEGMENT_MAX_ITEMS;
                const segmentEndIndex = Math.min(segmentStartIndex + this.SEGMENT_MAX_ITEMS, totalItems);
                const segmentItemCount = segmentEndIndex - segmentStartIndex;

                return segmentItemCount * this.itemHeight;
            }

            getTotalItemCount() {
                if (this.filteredIndices) {
                    return this.filteredIndices.length;
                }
                return this.totalItems;
            }

            manageChunks() {
                const currentChunk = Math.floor(this.virtualScrollTop / (this.CHUNK_SIZE * this.itemHeight));

                // 预加载策略
                for (let i = Math.max(0, currentChunk - this.PRELOAD_CHUNKS);
                     i <= Math.min(this.chunks.size - 1, currentChunk + this.PRELOAD_CHUNKS);
                     i++) {
                    this.loadChunk(i);
                }

                // 卸载远离的块
                for (const chunkIndex of this.loadedChunks) {
                    if (Math.abs(chunkIndex - currentChunk) > this.PRELOAD_CHUNKS + this.KEEP_CHUNKS) {
                        this.unloadChunk(chunkIndex);
                    }
                }

                this.currentChunk = currentChunk;
            }

            handleResize() {
                this.containerHeight = this.container.clientHeight;
                this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.buffer * 2;
                this.render();
            }

            render() {
                if (this.totalItems === 0) {
                    this.content.innerHTML = '<div class="loading">没有数据</div>';
                    return;
                }

                const visibleItems = this.getVisibleItems();

                if (visibleItems.length === 0) {
                    this.content.innerHTML = '<div class="chunk-loading">加载中...</div>';
                    return;
                }

                // 更新startIndex用于相对位置计算
                if (visibleItems.length > 0) {
                    this.startIndex = visibleItems[0].virtualIndex;
                }

                // 计算当前段的高度
                const currentSegmentHeight = this.getCurrentSegmentHeight();
                this.content.style.height = `${currentSegmentHeight}px`;

                // 清空现有内容
                this.content.innerHTML = '';

                // 渲染可见项目
                visibleItems.forEach(({ item, virtualIndex, realIndex }) => {
                    const itemElement = this.createItemElement(item, virtualIndex, realIndex);
                    this.content.appendChild(itemElement);
                });
                this.updateStats();
            }

            getVisibleItems() {
                const visibleItems = [];
                const viewStart = this.virtualScrollTop;
                const viewEnd = this.virtualScrollTop + this.containerHeight + (this.buffer * this.itemHeight);

                if (this.filteredIndices) {
                    // 搜索模式 - 需要考虑分段
                    this.filteredIndices.forEach((globalIndex, filteredIndex) => {
                        const itemTop = filteredIndex * this.itemHeight;
                        const itemBottom = itemTop + this.itemHeight;

                        if (itemBottom >= viewStart && itemTop <= viewEnd) {
                            const item = this.getItemByGlobalIndex(globalIndex);
                            if (item) {
                                visibleItems.push({
                                    item,
                                    virtualIndex: filteredIndex,
                                    realIndex: globalIndex
                                });
                            }
                        }
                    });
                } else {
                    // 正常模式 - 分段处理
                    const segmentStartIndex = this.currentSegment * this.SEGMENT_MAX_ITEMS;
                    const segmentEndIndex = Math.min(segmentStartIndex + this.SEGMENT_MAX_ITEMS, this.totalItems);

                    const startIndex = Math.max(segmentStartIndex, Math.floor(viewStart / this.itemHeight) - this.buffer);
                    const endIndex = Math.min(segmentEndIndex - 1,
                        Math.ceil(viewEnd / this.itemHeight) + this.buffer);

                    for (let i = startIndex; i <= endIndex; i++) {
                        const item = this.getItemByGlobalIndex(i);
                        if (item) {
                            visibleItems.push({
                                item,
                                virtualIndex: i,
                                realIndex: i
                            });
                        }
                    }
                }

                return visibleItems;
            }

            getItemByGlobalIndex(globalIndex) {
                const chunkIndex = Math.floor(globalIndex / this.CHUNK_SIZE);
                const localIndex = globalIndex % this.CHUNK_SIZE;

                const chunk = this.chunks.get(chunkIndex);
                if (!chunk || !chunk.loaded || !chunk.data[localIndex]) {
                    return null;
                }

                return chunk.data[localIndex];
            }

            createItemElement(item, virtualIndex, realIndex) {
                const element = document.createElement('div');
                element.className = 'virtual-list-item';

                // 计算在当前段内的相对位置
                const globalVirtualTop = virtualIndex * this.itemHeight;
                const segmentRelativeTop = globalVirtualTop - this.segmentOffset;

                element.style.top = `${segmentRelativeTop}px`;
                element.style.height = `${this.itemHeight}px`;

                const highlightedTitle = this.highlightText(item.title, this.searchTerm);
                const highlightedSubtitle = this.highlightText(item.subtitle, this.searchTerm);

                element.innerHTML = `
                    <div class="item-content">
                        <div class="item-avatar">${item.avatar}</div>
                        <div class="item-info">
                            <div class="item-title">${highlightedTitle}</div>
                            <div class="item-subtitle">${highlightedSubtitle}</div>
                        </div>
                    </div>
                    <div class="item-actions">
                        <button class="item-btn" onclick="editItem(${item.id})">编辑</button>
                        <button class="item-btn" onclick="deleteItem(${item.id})">删除</button>
                    </div>
                `;

                return element;
            }

            virtualToDomPosition(virtualTop) {
                // 这个方法现在不再需要，因为我们直接在createItemElement中计算位置
                return virtualTop;
            }

            highlightText(text, searchTerm) {
                if (!searchTerm) return text;

                const regex = new RegExp(`(${searchTerm})`, 'gi');
                return text.replace(regex, '<span class="highlight">$1</span>');
            }

            filter(searchTerm) {
                const startTime = performance.now();

                this.searchTerm = searchTerm.trim();

                if (!this.searchTerm) {
                    this.filteredIndices = null;
                } else {
                    // 构建搜索结果索引
                    this.filteredIndices = [];
                    let globalIndex = 0;

                    for (const [chunkIndex, chunk] of this.chunks) {
                        if (chunk.loaded) {
                            chunk.data.forEach((item, localIndex) => {
                                if (item.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                    item.subtitle.toLowerCase().includes(this.searchTerm.toLowerCase())) {
                                    this.filteredIndices.push(globalIndex);
                                }
                                globalIndex++;
                            });
                        } else {
                            globalIndex += chunk.data ? chunk.data.length : this.CHUNK_SIZE;
                        }
                    }
                }

                this.virtualScrollTop = 0;
                this.container.scrollTop = 0;
                this.render();
                this.updateStats();

                const endTime = performance.now();
                document.getElementById('renderTime').textContent = `${(endTime - startTime).toFixed(2)}ms`;
            }

            updateStats() {
                const totalCount = this.filteredIndices ? this.filteredIndices.length : this.totalItems;
                document.getElementById('totalCount').textContent = totalCount.toLocaleString();

                const visibleItems = this.getVisibleItems();
                document.getElementById('visibleCount').textContent = visibleItems.length;

                document.getElementById('currentChunk').textContent =
                    `${this.currentChunk + 1}/${this.chunks.size}`;
            }

            updateScrollInfo() {
                const totalVirtualHeight = this.getTotalVirtualHeight();
                const scrollPercent = totalVirtualHeight > 0 ?
                    ((this.virtualScrollTop / Math.max(1, totalVirtualHeight - this.containerHeight)) * 100).toFixed(1) : 0;

                const currentItem = Math.floor(this.virtualScrollTop / this.itemHeight) + 1;
                const totalCount = this.filteredIndices ? this.filteredIndices.length : this.totalItems;

                document.getElementById('scrollInfo').textContent =
                    `第 ${currentItem} 项 (${scrollPercent}%)`;
            }

            scrollToTop() {
                this.virtualScrollTop = 0;
                this.container.scrollTop = 0;
                this.render();
            }

            scrollToBottom() {
                const totalItems = this.getTotalItemCount();
                const lastItemIndex = totalItems - 1;

                // 跳转到最后一个项目
                this.jumpToItem(lastItemIndex);
            }

            jumpToItem(itemIndex) {
                const targetVirtualTop = itemIndex * this.itemHeight;

                // 计算目标项目所在的段
                const targetSegment = Math.floor(itemIndex / this.SEGMENT_MAX_ITEMS);

                // 切换到目标段
                this.currentSegment = targetSegment;
                this.segmentOffset = targetSegment * this.SEGMENT_MAX_ITEMS * this.itemHeight;

                // 计算在目标段内的滚动位置
                const segmentScrollTop = targetVirtualTop - this.segmentOffset;

                // 设置虚拟滚动位置和实际滚动位置
                this.virtualScrollTop = targetVirtualTop;
                this.container.scrollTop = segmentScrollTop;

                this.render();
            }
        }

        // 全局变量
        let virtualList;
        let currentData = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('virtualListContainer');
            virtualList = new InfiniteVirtualList(container, {
                itemHeight: 60,
                buffer: 5
            });

            // 搜索功能
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    virtualList.filter(this.value);
                }, 300);
            });

            // 跳转功能
            const jumpInput = document.getElementById('jumpInput');
            jumpInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpToItem();
                }
            });
        });

        // 生成测试数据
        function generateData(count) {
            const startTime = performance.now();

            currentData = [];

            for (let i = 0; i < count; i++) {
                currentData.push({
                    id: i + 1,
                    title: `项目 ${i + 1}`,
                    subtitle: `数据项 ${i + 1}`,
                    avatar: (i + 1).toString()
                });
            }

            const endTime = performance.now();
            console.log(`生成${count.toLocaleString()}条数据耗时: ${(endTime - startTime).toFixed(2)}ms`);

            virtualList.setData(currentData);

            // 清空搜索框
            document.getElementById('searchInput').value = '';
        }

        // 工具函数
        function scrollToTop() {
            virtualList.scrollToTop();
        }

        function scrollToBottom() {
            virtualList.scrollToBottom();
        }

        function jumpToItem() {
            const jumpInput = document.getElementById('jumpInput');
            const itemNumber = parseInt(jumpInput.value);

            if (isNaN(itemNumber) || itemNumber < 1) {
                alert('请输入有效的项目编号（从1开始）');
                return;
            }

            const totalCount = virtualList.filteredIndices ?
                virtualList.filteredIndices.length : virtualList.totalItems;

            if (itemNumber > totalCount) {
                alert(`项目编号不能超过 ${totalCount}`);
                return;
            }

            virtualList.jumpToItem(itemNumber - 1); // 转换为0基索引
            jumpInput.value = '';
        }

        function editItem(id) {
            alert(`编辑项目 ID: ${id}`);
        }

        function deleteItem(id) {
            if (confirm(`确定要删除项目 ID: ${id} 吗？`)) {
                const index = currentData.findIndex(item => item.id === id);
                if (index !== -1) {
                    currentData.splice(index, 1);
                    virtualList.setData(currentData);
                }
            }
        }

        // 性能监控
        setInterval(() => {
            if (virtualList && virtualList.totalItems > 0) {
                virtualList.updateScrollInfo();
            }
        }, 100);
    </script>
</body>
</html>